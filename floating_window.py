import sys
import atexit
import signal
from Cocoa import (
    NSApplication, NSView,NSImageView, NSImage, NSMakeRect, NSPoint,
    NSFloatingWindowLevel, NSBorderlessWindowMask, NSBackingStoreBuffered,
    NSWindowCollectionBehaviorCanJoinAllSpaces, NSWindowCollectionBehaviorFullScreenAuxiliary,
    NSApplicationActivationPolicyAccessory, NSPanel, NSButton, NSColor, NSBezelStyleRounded
)
from AppKit import (
    NSWindowStyleMaskTitled,
    NSWindowStyleMaskClosable,
    NSWindowStyleMaskResizable,
    NSBackingStoreBuffered,
    NSMakeRect
)

current_window = None  # Global reference

class DraggablePanel(NSPanel):
    def init(self):
        self = super(DraggablePanel, self).init()
        if self:
            self._initialLocation = NSPoint(0, 0)
        return self

    def mouseDown_(self, event):
        self._initialLocation = event.locationInWindow()

    def mouseDragged_(self, event):
        screen_frame = self.screen().frame()
        window_frame = self.frame()
        current_location = event.locationInWindow()

        new_origin = NSPoint(
            window_frame.origin.x + (current_location.x - self._initialLocation.x),
            window_frame.origin.y + (current_location.y - self._initialLocation.y)
        )

        # Clamp to screen bounds
        if (new_origin.y + window_frame.size.height) > (screen_frame.origin.y + screen_frame.size.height):
            new_origin.y = screen_frame.origin.y + (screen_frame.size.height - window_frame.size.height)

        self.setFrameOrigin_(new_origin)

def cleanup():
    global current_window
    if current_window:
        print("🧹 Cleaning up floating window...")
        current_window.close()
        current_window = None

def create_overlay(image_path):
    global current_window

    app = NSApplication.sharedApplication()
    app.setActivationPolicy_(NSApplicationActivationPolicyAccessory)

    # Load the image
    image = NSImage.alloc().initWithContentsOfFile_(image_path)
    if not image:
        print("❌ Could not load image:", image_path)
        sys.exit(1)

    width = image.size().width
    height = image.size().height

    # Create container view
    container_view = NSView.alloc().initWithFrame_(NSMakeRect(0, 0, width, height))

    image_view = NSImageView.alloc().initWithFrame_(NSMakeRect(0, 0, width, height))
    image_view.setImage_(image)
    container_view.addSubview_(image_view)

    # Close Button
    close_button = NSButton.alloc().initWithFrame_(NSMakeRect(width - 20, height - 20, 16, 16))
    close_button.setTitle_("✕")
    close_button.setBordered_(False)
    close_button.setBezelStyle_(NSBezelStyleRounded)
    close_button.setWantsLayer_(True)
    close_button.layer().setBackgroundColor_(NSColor.redColor().CGColor())
    close_button.layer().setCornerRadius_(8)
    close_button.setTarget_(container_view)
    close_button.setAction_("removeFromSuperview")  # You can also connect to a function that calls `NSApp.terminate_()`

    def close_window(_):
        panel.close()
        NSApplication.sharedApplication().terminate_(None)

    close_button.setTarget_(close_button)
    close_button.setAction_("performClick:")
    close_button.setTarget_(close_window)
    container_view.addSubview_(close_button)

    # Set up panel
    mask = NSWindowStyleMaskTitled | NSWindowStyleMaskClosable
    panel = DraggablePanel.alloc().initWithContentRect_styleMask_backing_defer_(
        NSMakeRect(300, 300, width, height),
        mask,
        NSBackingStoreBuffered,
        False
    )

    panel.setContentView_(image_view)
    panel.setLevel_(NSFloatingWindowLevel)
    panel.setOpaque_(False)
    panel.setHasShadow_(True)
    panel.setAlphaValue_(0.95)
    panel.setIgnoresMouseEvents_(False)
    panel.setCollectionBehavior_(
        NSWindowCollectionBehaviorCanJoinAllSpaces |
        NSWindowCollectionBehaviorFullScreenAuxiliary
    )

    current_window = panel
    panel.orderFrontRegardless()

    # Set up cleanup hooks
    atexit.register(cleanup)
    signal.signal(signal.SIGINT, lambda sig, frame: sys.exit(0))  # Handle Ctrl+C
    signal.signal(signal.SIGTERM, lambda sig, frame: sys.exit(0))  # Handle kill

    app.run()

if __name__ == "__main__":
    image_path = "assets/captcha.png"
    create_overlay(image_path)
