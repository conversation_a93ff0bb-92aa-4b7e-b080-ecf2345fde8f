import tkinter as tk
from PIL import Image, ImageTk
from utils.audio import play_alert

class FloatingWindow:
    def __init__(self, browser_controller):
        self.browser = browser_controller

    def show(self):
        self.browser.save_captcha_image()
        coords = self.browser.get_area_coordinates()

        play_alert()
        root = tk.Tk()
        root.title("Captcha")
        root.geometry("+100+100")
        root.attributes("-topmost", True)

        img = Image.open("assets/captcha.png")
        img = img.resize((280, 60))  # Resize for display if needed
        tk_img = ImageTk.PhotoImage(img)

        label = tk.Label(root, image=tk_img)
        label.pack()

        def on_click(event):
            x, y = event.x, event.y
            print(f"Clicked at: {x}, {y}")
            index = self.map_click_to_area(x, y, coords, img.size, display_size=(280, 60))
            if index is not None:
                print("Mapped to index:", index)
                self.browser.simulate_click(index)
            else:
                print("Click not mapped.")
            root.destroy()

        label.bind("<Button-1>", on_click)
        root.mainloop()

    def map_click_to_area(self, x, y, coords_list, original_size, display_size):
        scale_x = original_size[0] / display_size[0]
        scale_y = original_size[1] / display_size[1]
        click_x = x * scale_x
        click_y = y * scale_y

        for i, (x1, y1, x2, y2) in enumerate(coords_list):
            if x1 <= click_x <= x2 and y1 <= click_y <= y2:
                return i
        return None
