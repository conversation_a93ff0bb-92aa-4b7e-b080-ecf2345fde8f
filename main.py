from headless_browser import <PERSON>rowserController
from float_window import FloatingWindow
import time

def main():
    browser = BrowserController()
    browser.start_browser_with_login()

    print("Monitoring started...")

    while True:
        if browser.is_captcha_ready() and browser.is_timer_complete():
            FloatingWindow(browser).show()
            time.sleep(5)  # Wait to avoid duplicate windows
        else:
            time.sleep(1)

if __name__ == "__main__":
    main()
