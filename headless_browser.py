from selenium import webdriver
import base64
import requests
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import pyautogui
import time

class BrowserController:
    def __init__(self):
        self.driver = None

    def start_browser_with_login(self):
        options = Options()
        options.headless = False  # Use GUI mode to login
        self.driver = webdriver.Chrome(options=options)
        self.driver.get("http://www.easyhits4u.com/")
        input("Login manually and press Enter to continue...")
        self.driver.get("http://www.easyhits4u.com/surf/?surftype=2&q=start")

    def is_captcha_ready(self):
        try:
            img = self.driver.find_element(By.ID, "surf_image")
            return img.is_displayed()
        except:
            return False

    def is_timer_complete(self):
        try:
            bar = self.driver.find_element(By.CSS_SELECTOR, ".progress-bar .progress")
            style = bar.get_attribute("style")  # example: width: 100%;
            width_percent = float(style.split("width:")[1].split("%")[0].strip())
            return width_percent >= 100
        except:
            return False

    # def simulate_click(self, index):
    #     try:
    #         areas = self.driver.find_elements(By.CSS_SELECTOR, "map[name='surfmap'] area")
    #         if 0 <= index < len(areas):
    #             coords = areas[index].get_attribute("coords")  # x1,y1,x2,y2
    #             x1, y1, x2, y2 = map(int, coords.split(','))
    #             center_x = (x1 + x2) // 2
    #             center_y = (y1 + y2) // 2

    #             # Get position of the image
    #             img = self.driver.find_element(By.ID, "surf_image")
    #             loc = img.location
    #             abs_x = loc['x'] + center_x
    #             abs_y = loc['y'] + center_y

    #             # Bring browser window to front and click
    #             pyautogui.moveTo(abs_x, abs_y)
    #             pyautogui.click()
    #     except Exception as e:
    #         print("Click simulation failed:", e)

    def simulate_click(self, index):
        try:
            self.driver.execute_script(
                f"document.getElementsByTagName('area')[{index}].click()"
            )
        except Exception as e:
            print("JS click failed:", e)

    def save_captcha_image(self, path="assets/captcha.png"):
        img = self.driver.find_element(By.ID, "surf_image")
        src = img.get_attribute("src")

        if src.startswith("data:image"):
            # Base64 image
            base64_data = src.split(",")[1]
            with open(path, "wb") as f:
                f.write(base64.b64decode(base64_data))
        else:
            # Relative or full URL
            if src.startswith("/"):
                src = "http://www.easyhits4u.com/" + src
            r = requests.get(src)
            with open(path, "wb") as f:
                f.write(r.content)

    def get_area_coordinates(self):
        areas = self.driver.find_elements(By.CSS_SELECTOR, "map[name='surfmap'] area")
        coords_list = []
        for a in areas:
            coords = a.get_attribute("coords")
            coords_list.append(tuple(map(int, coords.split(","))))
        return coords_list